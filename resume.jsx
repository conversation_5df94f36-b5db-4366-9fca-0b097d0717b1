import React from 'react';
import { Mail, Phone, MapPin, Linkedin, Github, ExternalLink } from 'lucide-react';

// Main App Component
const App = () => {
  // Data extracted from the provided resume images
  const resumeData = {
    personal_info: {
      name: "<PERSON><PERSON> Bharucha",
      title: "Software Engineer",
      summary: "Dynamic Python Developer skilled in building and optimizing AWS Glue data pipelines, including Lambda-based data quality checks. Proficient in SQL for BI ETL and experienced in React web development (built one website from scratch, contributed to three others). Actively explores AI tools to innovate software development."
    },
    contact_info: {
      email: "<EMAIL>",
      phone: "+44 7721541515",
      location: "London, United Kingdom",
      linkedin: "linkedin.com/in/taha-bharucha",
      github: "github.com/Taha-1005"
    },
    education: [
      {
        degree: "B.Tech, Computer Science Engineering",
        institution: "Nirma University - Semester 7",
        dates: "06/2019 - 06/2023",
        grade: "GPA: 8.06"
      },
      {
        degree: "Higher Secondary",
        institution: "Delhi Public School, Kuwait",
        dates: "04/2017 - 04/2019",
        grade: "92.4%"
      }
    ],
    skills: [
      { name: "OOP", proficiency: 5 },
      { name: "Micro-services", proficiency: 4 },
      { name: "DSA", proficiency: 4 },
      { name: "React", proficiency: 4 },
      { name: "DBMS", proficiency: 5 }
    ],
    work_experience: [
      {
        role: "Software Engineer",
        company: "J.P.Morgan Chase & Co.",
        dates: "09/2023 - Present",
        location: "London, United Kingdom",
        achievements: [
          "Engineered Python-based features within AWS Glue data pipelines to support evolving data requirements.",
          "Automated data quality validation by developing AWS Lambda functions, improving data integrity.",
          "Optimized foundational SQL queries that drive BI ETL processes, enhancing data flow efficiency."
        ],
        contact: "Alex Sohrabkhani - <EMAIL>"
      },
      {
        role: "Software Engineer Intern",
        company: "J.P.Morgan Chase & Co.",
        dates: "04/2022 - 08/2022",
        location: "London, United Kingdom",
        achievements: [
          "Worked on micro-services to add new features in the Portfolio Management App. Tested the features using Cucumber Tests.",
          "Improved code quality of existing packages. Used plugins like pmd, pitest, Jacoco etc. to do so.",
          "Got hands on experience on writing tests with mocks, integration/unit tests, functional programming, JAVA and the Spring framework."
        ],
        contact: "Blerina Kapllani - <EMAIL>"
      },
      {
        role: "React Developer",
        company: "Indexx.ai",
        dates: "02/2023 - 04/2023",
        location: "Remote",
        company_description: "Company focused on making crypto and web3 access easy for everyone. Providing innovative solutions to current crypto problems.",
        achievements: [
          "Developed a new react website from scratch.",
          "Contributed to two other existing react websites."
        ]
      },
      {
        role: "Software Engineer Intern",
        company: "SuccessKPI Pvt Ltd",
        dates: "01/2023 - 06/2023",
        location: "Remote",
        company_description: "Provides software solutions to Contact Centers",
        achievements: [
          "Automate the testing for the Product of SuccessKPI.",
          "Used HTML/CSS, Python and Selenium.",
          "Developed and maintained tests."
        ],
        contact: "Mr. Naresh Chaudhari - <EMAIL>"
      }
    ],
    projects: [
      {
        title: "Blockchain based Placement",
        dates: "08/2022 - 12/2022",
        description: "Developed a full-stack blockchain project. Solution for college placement system to prevent unnecessary occupancy of placement by student.",
        details: [
          "Portals: Student, Company, Faculty.",
          "Functionalities: Apply For Company, LOR to Faculty, Respective requests.",
          "Smart contract in Solidity. Front-end- React."
        ]
      }
    ],
    certificates: [
      { name: "AWS Certified Solutions Architect - Associate", dates: "03/2025 - 03/2028" },
      { name: "AWS Certified Cloud Practitioner" },
      { name: "First Prize in MiNed Hackathon", dates: "03/2022 - 03/2022" }
    ],
    languages: [
      { name: "English", proficiency: "Native or Bilingual Proficiency" },
      { name: "Hindi", proficiency: "Professional Working Proficiency" }
    ],
    qualities: [
      "Leadership", "Team Work", "Time Management", "Communication", "Positive Outlook", "Desire to Learn"
    ]
  };

  return (
    <div className="font-['Inter'] bg-gray-50 min-h-screen p-4 sm:p-8">
      <div className="max-w-4xl mx-auto bg-white shadow-xl rounded-2xl overflow-hidden">
        {/* Header Section */}
        <div className="bg-gradient-to-r from-gray-800 to-gray-700 text-white p-6 sm:p-8 rounded-t-2xl">
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6">
            <div>
              <h1 className="text-4xl sm:text-5xl font-extrabold tracking-tight mb-1">{resumeData.personal_info.name}</h1>
              <p className="text-xl sm:text-2xl font-light text-gray-300">{resumeData.personal_info.title}</p>
            </div>
            <div className="mt-4 sm:mt-0 text-sm sm:text-base text-right">
              <ContactItem icon={<Mail size={16} />} text={resumeData.contact_info.email} link={`mailto:${resumeData.contact_info.email}`} />
              <ContactItem icon={<Phone size={16} />} text={resumeData.contact_info.phone} link={`tel:${resumeData.contact_info.phone}`} />
              <ContactItem icon={<MapPin size={16} />} text={resumeData.contact_info.location} />
              <ContactItem icon={<Linkedin size={16} />} text="LinkedIn" link={`https://${resumeData.contact_info.linkedin}`} />
              <ContactItem icon={<Github size={16} />} text="GitHub" link={`https://github.com/${resumeData.contact_info.github}`} />
            </div>
          </div>
          <p className="text-gray-200 text-base sm:text-lg leading-relaxed mt-4">
            {resumeData.personal_info.summary}
          </p>
        </div>

        {/* Main Content Area */}
        <div className="p-6 sm:p-8 grid grid-cols-1 md:grid-cols-3 gap-8">
          {/* Left Column for Education, Skills, Languages, Qualities */}
          <div className="md:col-span-1 space-y-8">
            {/* Education */}
            <Section title="EDUCATION">
              {resumeData.education.map((edu, index) => (
                <div key={index} className="mb-4 last:mb-0">
                  <h3 className="font-semibold text-lg text-gray-800">{edu.degree}</h3>
                  <p className="text-gray-700 text-sm">{edu.institution}</p>
                  <p className="text-gray-500 text-xs">{edu.dates}</p>
                  <p className="text-gray-600 text-xs font-medium">{edu.grade}</p>
                </div>
              ))}
            </Section>

            {/* Skills */}
            <Section title="SKILLS">
              {resumeData.skills.map((skill, index) => (
                <SkillItem key={index} name={skill.name} proficiency={skill.proficiency} />
              ))}
            </Section>

            {/* Languages */}
            <Section title="LANGUAGES">
              {resumeData.languages.map((lang, index) => (
                <div key={index} className="flex justify-between items-center mb-2 last:mb-0">
                  <span className="text-gray-800 font-medium">{lang.name}</span>
                  <span className="text-gray-600 text-sm">{lang.proficiency}</span>
                </div>
              ))}
            </Section>

            {/* Qualities */}
            <Section title="QUALITIES">
              <div className="flex flex-wrap gap-2">
                {resumeData.qualities.map((quality, index) => (
                  <span key={index} className="px-3 py-1 bg-blue-100 text-blue-800 text-xs font-medium rounded-full shadow-sm">
                    {quality}
                  </span>
                ))}
              </div>
            </Section>
          </div>

          {/* Right Column for Work Experience, Projects, Certificates */}
          <div className="md:col-span-2 space-y-8">
            {/* Work Experience */}
            <Section title="WORK EXPERIENCE">
              {resumeData.work_experience.map((job, index) => (
                <div key={index} className="mb-6 pb-4 border-b border-gray-200 last:border-b-0 last:mb-0">
                  <div className="flex justify-between items-baseline mb-1">
                    <h3 className="text-lg font-semibold text-gray-800">{job.role} at {job.company}</h3>
                    <span className="text-gray-500 text-sm">{job.dates}</span>
                  </div>
                  <p className="text-gray-600 text-sm mb-2">{job.location}</p>
                  {job.company_description && <p className="text-gray-700 text-sm italic mb-2">{job.company_description}</p>}
                  <ul className="list-disc list-inside text-gray-700 text-sm space-y-1">
                    {job.achievements.map((achievement, i) => (
                      <li key={i}>{achievement}</li>
                    ))}
                  </ul>
                  {job.contact && <p className="text-gray-600 text-xs mt-2">Contact: {job.contact}</p>}
                </div>
              ))}
            </Section>

            {/* Projects */}
            <Section title="PROJECTS">
              {resumeData.projects.map((project, index) => (
                <div key={index} className="mb-6 pb-4 border-b border-gray-200 last:border-b-0 last:mb-0">
                  <div className="flex justify-between items-baseline mb-1">
                    <h3 className="text-lg font-semibold text-gray-800">
                      {project.title}
                      {project.link && <a href={project.link} target="_blank" rel="noopener noreferrer" className="ml-2 text-blue-500 hover:text-blue-700"><ExternalLink size={16} /></a>}
                    </h3>
                    <span className="text-gray-500 text-sm">{project.dates}</span>
                  </div>
                  <p className="text-gray-700 text-sm mb-2">{project.description}</p>
                  <ul className="list-disc list-inside text-gray-700 text-sm space-y-1">
                    {project.details.map((detail, i) => (
                      <li key={i}>{detail}</li>
                    ))}
                  </ul>
                </div>
              ))}
            </Section>

            {/* Certificates */}
            <Section title="CERTIFICATES">
              {resumeData.certificates.map((cert, index) => (
                <div key={index} className="flex justify-between items-baseline mb-2 last:mb-0">
                  <h3 className="text-gray-800 font-medium">{cert.name}</h3>
                  {cert.dates && <span className="text-gray-500 text-sm">{cert.dates}</span>}
                </div>
              ))}
            </Section>
          </div>
        </div>
      </div>
    </div>
  );
};

// Helper Component for Section Titles
const Section = ({ title, children }) => (
  <div>
    <h2 className="text-xl sm:text-2xl font-bold text-gray-800 border-b-2 border-blue-500 pb-2 mb-4">
      {title}
    </h2>
    {children}
  </div>
);

// Helper Component for Contact Info Items
const ContactItem = ({ icon, text, link }) => (
  <div className="flex items-center justify-end gap-2 text-gray-300 mb-1">
    {icon}
    {link ? (
      <a href={link} target="_blank" rel="noopener noreferrer" className="hover:underline">
        {text}
      </a>
    ) : (
      <span>{text}</span>
    )}
  </div>
);

// Helper Component for Skill Items with Proficiency Dots
const SkillItem = ({ name, proficiency }) => {
  const dots = Array.from({ length: 5 }, (_, i) => i < proficiency); // Array of booleans [True, True, False, False, False]

  return (
    <div className="flex justify-between items-center mb-2">
      <span className="text-gray-800 font-medium">{name}</span>
      <div className="flex gap-1">
        {dots.map((filled, i) => (
          <span
            key={i}
            className={`w-3 h-3 rounded-full ${
              filled ? 'bg-blue-500 shadow-md' : 'bg-gray-300'
            }`}
          ></span>
        ))}
      </div>
    </div>
  );
};

export default App;
